<script lang="ts">
	import ToolCard from '$lib/components/ToolCard.svelte';
	import FeaturedTools from '$lib/components/FeaturedTools.svelte';
	import Head from '$lib/components/Head.svelte';
	import { ExternalLink } from 'lucide-svelte';
	import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	// Helper function to get metadata value
	function getMetaValue(key: string): string {
		return data.metadata[key] || '';
	}

	// Helper function to get metadata value for related items
	function getMetaValueForItem(item: any, key: string): string {
		if (!item.metadata) return '';
		const meta = item.metadata.find((m: any) => m.item_meta_key === key);
		return meta ? meta.item_meta_value || '' : '';
	}

	// Helper function to get term color for related items (default for now)
	function getTermColor(item: any, taxonomy: string): string {
		// For now return default color, will enhance with term_color metadata later
		return '#3730a3';
	}

	// Helper function to get thumbnail URL for related items
	function getThumbnailUrlForItem(item: any): string {
		if (!item.metadata) return '/assets/images/default-screenshot.jpg';
		const meta = item.metadata.find((m: any) => m.item_meta_key === 'thumbnail_url');
		return meta && meta.item_meta_value ? meta.item_meta_value : '/assets/images/default-screenshot.jpg';
	}

	// Helper function to get category slug for related items
	function getCategorySlugForItem(item: any): string {
		if (!item.terms) return '';
		const categoryTerm = item.terms.find((t: any) => t.term_taxonomy === 'category');
		return categoryTerm?.term_slug || '';
	}

	// Helper function to get pricing slug for related items
	function getPricingSlugForItem(item: any): string {
		if (!item.terms) return '';
		const pricingTerm = item.terms.find((t: any) => t.term_taxonomy === 'pricing');
		return pricingTerm?.term_slug || '';
	}

	// Helper function to get category name for related items
	function getCategoryName(item: any): string {
		if (!item.terms) return '';
		const categoryTerm = item.terms.find((t: any) => t.term_taxonomy === 'category');
		return categoryTerm?.term_name || '';
	}

	// Helper function to get pricing name for related items
	function getPricingName(item: any): string {
		if (!item.terms) return '';
		const pricingTerm = item.terms.find((t: any) => t.term_taxonomy === 'pricing');
		return pricingTerm?.term_name || '';
	}

	// Helper function to get categories for current item
	function getCategories(): any[] {
		if (!data.terms) return [];
		return data.terms.filter((t: any) => t.term_taxonomy === 'category');
	}

	// Helper function to get pricing for current item
	function getPricing(): any[] {
		if (!data.terms) return [];
		return data.terms.filter((t: any) => t.term_taxonomy === 'pricing');
	}

	// Helper function to determine if links should be dofollow
	function isDofollow(): boolean {
		return getMetaValue('dofollow') === '1';
	}

	// Format date - handle timestamp properly
	function formatDate(timestamp: number): string {
		// Convert timestamp to milliseconds if it's in seconds
		const date = new Date(timestamp * 1000);
		return date.toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric'
		});
	}
</script>

<Head
	title="{data.item.item_name} - {PUBLIC_SITE_NAME}"
	description={getMetaValue('description') || data.item.item_name}
	url="{PUBLIC_SITE_URL}/ai/{data.item.item_slug}"
/>

<div class="container mx-auto py-12 px-4 sm:px-6">
	<div class="grid grid-cols-1 lg:grid-cols-12 gap-12">
		<!-- Left Column: Tool Details -->
		<div class="lg:col-span-8">
			<!-- Tool Header -->
			<div class="flex flex-wrap items-center justify-between gap-4 mb-6">
				<h1 class="text-3xl md:text-4xl font-bold text-blue-900">{data.item.item_name}</h1>
				<a href={data.item.item_url} target="_blank" rel={isDofollow() ? "noopener noreferrer" : "noopener noreferrer nofollow"} class="px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2 group">
					Visit
					<ExternalLink class="w-4 h-4 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform" />
				</a>
			</div>
			<p class="text-lg text-gray-600 mb-8 leading-relaxed">{getMetaValue('description') || 'AI tool for productivity'}</p>

			<!-- Metadata -->
			<div class="bg-white border border-gray-200 rounded-sm p-6 mb-10 shadow-sm">
				<div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
					<div>
						<p class="font-semibold text-gray-900 mb-1">Published on</p>
						<p class="text-gray-600 text-sm">{formatDate(data.item.item_created_at)}</p>
					</div>
					<div>
						<p class="font-semibold text-gray-900 mb-1">Website</p>
						<a href={data.item.item_url} target="_blank" rel={isDofollow() ? "noopener noreferrer" : "noopener noreferrer nofollow"} class="text-blue-600 hover:underline text-sm">
							{data.item.item_url}
						</a>
					</div>
					{#if getCategories().length > 0}
						<div class="md:col-span-2">
							<p class="font-semibold text-gray-900 mb-1">Categories</p>
							<div class="flex flex-wrap gap-2">
								{#each getCategories() as category}
									<a href="/category/{category.term_slug}" class="inline-block bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full hover:bg-blue-200 transition-colors">
										{category.term_name}
									</a>
								{/each}
							</div>
						</div>
					{/if}
					{#if getPricing().length > 0}
						<div class="md:col-span-2">
							<p class="font-semibold text-gray-900 mb-2">Pricing</p>
							<div class="flex flex-wrap gap-2">
								{#each getPricing() as pricing}
									<a href="/pricing/{pricing.term_slug}" class="inline-block bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full hover:bg-green-200 transition-colors">
										{pricing.term_name}
									</a>
								{/each}
							</div>
						</div>
					{/if}
				</div>
			</div>

			<!-- Tool Screenshot -->
			<div class="mb-12">
				<img
					src={getMetaValue('screenshot_url') || '/assets/images/default-screenshot.jpg'}
					alt="{data.item.item_name} application interface and features"
					class="rounded-xl border border-gray-200 shadow-md w-full"
					loading="lazy"
					decoding="async"
				/>
			</div>

			<!-- Content Sections -->
			<div class="space-y-10 prose max-w-none">
				{#if getMetaValue('content') || getMetaValue('description')}
					<section>
						<h2 class="text-4xl font-bold text-primary-blue mb-6">About {data.item.item_name}</h2>
						{#each (getMetaValue('content') || getMetaValue('description')).split('\n\n') as paragraph}
							{#if paragraph.trim()}
								<p class="text-gray-700 leading-relaxed mb-4 whitespace-pre-line">{paragraph}</p>
							{/if}
						{/each}
					</section>
				{/if}
			</div>

			<!-- You may also like -->
			{#if data.relatedItems.length > 0}
				<section class="mt-16">
					<h2 class="text-3xl font-bold mb-6 text-primary-blue">You may also like:</h2>
					<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
						{#each data.relatedItems as item}
							<ToolCard
								name={item.item_name}
								description={getMetaValueForItem(item, 'description') || 'AI tool for productivity'}
								category={getCategoryName(item)}
								price={getPricingName(item)}
								imageColor="e0e7ff/3730a3"
								categoryColor={getTermColor(item, 'category')}
								slug={item.item_slug}
								thumbnailUrl={getThumbnailUrlForItem(item)}
								categorySlug={getCategorySlugForItem(item)}
								pricingSlug={getPricingSlugForItem(item)}
								itemUrl={item.item_url}
							/>
						{/each}
					</div>
					<div class="text-center mt-8">
						<a href={data.firstCategorySlug ? `/category/${data.firstCategorySlug}` : '/'} class="px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-200">
							{data.firstCategorySlug ? 'More from this Category' : 'More AI Tools'}
						</a>
					</div>
				</section>
			{/if}
		</div>

		<!-- Right Column: Sidebar -->
		<aside class="lg:col-span-4">
			<FeaturedTools items={data.featuredItems || []} />
		</aside>
	</div>
</div>
