<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import ToolCard from '$lib/components/ToolCard.svelte';
	import Head from '$lib/components/Head.svelte';
	import { Search, ChevronLeft, ChevronRight } from 'lucide-svelte';
	import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
	import { goto } from '$app/navigation';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	// Search input state
	let searchQuery = $state(data.query);

	// Helper function to get metadata value
	function getMetaValue(item: any, key: string): string {
		if (!item.metadata) return '';
		const meta = item.metadata.find((m: any) => m.item_meta_key === key);
		return meta ? meta.item_meta_value || '' : '';
	}

	// Helper function to get category name
	function getCategoryName(item: any): string {
		if (!item.terms) return '';
		const category = item.terms.find((t: any) => t.term_taxonomy === 'category');
		return category ? category.term_name : '';
	}

	// Helper function to get pricing name
	function getPricingName(item: any): string {
		if (!item.terms) return '';
		const pricing = item.terms.find((t: any) => t.term_taxonomy === 'pricing');
		return pricing ? pricing.term_name : '';
	}

	// Helper function to get category color
	function getTermColor(item: any, taxonomy: string): string {
		if (!item.terms) return '#3730a3';
		const term = item.terms.find((t: any) => t.term_taxonomy === taxonomy);
		return term?.term_color || '#3730a3';
	}

	// Helper function to get thumbnail URL
	function getThumbnailUrl(item: any): string {
		const thumbnailUrl = getMetaValue(item, 'thumbnail_url');
		const iconUrl = getMetaValue(item, 'icon_url');
		const screenshotUrl = getMetaValue(item, 'screenshot_url');
		
		return thumbnailUrl || iconUrl || screenshotUrl || '/assets/images/default-screenshot.jpg';
	}

	// Helper function to get category slug
	function getCategorySlug(item: any): string {
		if (!item.terms) return '';
		const category = item.terms.find((t: any) => t.term_taxonomy === 'category');
		return category ? category.term_slug : '';
	}

	// Helper function to get pricing slug
	function getPricingSlug(item: any): string {
		if (!item.terms) return '';
		const pricing = item.terms.find((t: any) => t.term_taxonomy === 'pricing');
		return pricing ? pricing.term_slug : '';
	}

	// Handle search form submission
	function handleSearch(event: Event) {
		event.preventDefault();
		if (searchQuery.trim()) {
			goto(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
		}
	}
</script>

<Head
	title="Search Results{data.query ? ` for "${data.query}"` : ''} - {PUBLIC_SITE_NAME}"
	description="Search results for AI tools and websites."
	url="{PUBLIC_SITE_URL}/search{data.query ? `?q=${encodeURIComponent(data.query)}` : ''}"
/>

<div class="container mx-auto py-12 px-4 sm:px-6">
	<!-- Search Header -->
	<div class="text-center mb-12">
		<h1 class="text-3xl md:text-4xl font-bold text-blue-900 mb-6">
			{#if data.query}
				Search Results for "{data.query}"
			{:else}
				Search AI Tools
			{/if}
		</h1>
		
		<!-- Search Form -->
		<div class="max-w-2xl mx-auto relative mb-8">
			<form onsubmit={handleSearch}>
				<Input
					type="text"
					bind:value={searchQuery}
					placeholder="Search for a tool, e.g., 'image generator'..."
					class="w-full h-auto py-4 pl-12 pr-4 rounded-full text-gray-800 bg-white shadow-md focus:border-blue-900 focus:ring-2 focus:ring-blue-900/30 transition-all duration-300"
				/>
				<Search class="absolute left-5 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
			</form>
		</div>

		{#if data.query}
			<p class="text-lg text-gray-600">
				Found {data.total} result{data.total !== 1 ? 's' : ''}
			</p>
		{/if}
	</div>

	<!-- Search Results -->
	{#if data.items && data.items.length > 0}
		<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
			{#each data.items as item}
				<ToolCard
					name={item.item_name}
					description={getMetaValue(item, 'description') || 'AI tool for productivity'}
					category={getCategoryName(item)}
					price={getPricingName(item)}
					imageColor="e0e7ff/3730a3"
					categoryColor={getTermColor(item, 'category')}
					slug={item.item_slug}
					thumbnailUrl={getThumbnailUrl(item)}
					categorySlug={getCategorySlug(item)}
					pricingSlug={getPricingSlug(item)}
					itemUrl={item.item_url}
				/>
			{/each}
		</div>

		<!-- Pagination -->
		{#if data.totalPages > 1}
			<div class="flex justify-center items-center gap-4">
				{#if data.hasPrevPage}
					<Button 
						href="/search?q={encodeURIComponent(data.query)}&page={data.currentPage - 1}" 
						variant="outline" 
						class="flex items-center gap-2"
					>
						<ChevronLeft class="w-4 h-4" />
						Previous
					</Button>
				{/if}
				
				<span class="text-gray-600">
					Page {data.currentPage} of {data.totalPages}
				</span>
				
				{#if data.hasNextPage}
					<Button 
						href="/search?q={encodeURIComponent(data.query)}&page={data.currentPage + 1}" 
						variant="outline" 
						class="flex items-center gap-2"
					>
						Next
						<ChevronRight class="w-4 h-4" />
					</Button>
				{/if}
			</div>
		{/if}
	{:else if data.query}
		<!-- No Results -->
		<div class="text-center py-16">
			<Search class="w-16 h-16 text-gray-300 mx-auto mb-4" />
			<h3 class="text-xl font-semibold text-gray-600 mb-2">No Results Found</h3>
			<p class="text-gray-500 mb-6">
				We couldn't find any AI tools matching "{data.query}". Try different keywords or browse our categories.
			</p>
			<div class="flex flex-wrap justify-center gap-4">
				<Button href="/" variant="outline">Browse All Tools</Button>
				<Button href="/categories" variant="outline">View Categories</Button>
				<Button href="/featured" variant="outline">Featured Tools</Button>
			</div>
		</div>
	{:else}
		<!-- Empty Search State -->
		<div class="text-center py-16">
			<Search class="w-16 h-16 text-gray-300 mx-auto mb-4" />
			<h3 class="text-xl font-semibold text-gray-600 mb-2">Start Your Search</h3>
			<p class="text-gray-500 mb-6">
				Enter keywords to find the perfect AI tools for your needs.
			</p>
			<div class="flex flex-wrap justify-center gap-4">
				<Button href="/" variant="outline">Browse All Tools</Button>
				<Button href="/categories" variant="outline">View Categories</Button>
				<Button href="/featured" variant="outline">Featured Tools</Button>
			</div>
		</div>
	{/if}
</div>
